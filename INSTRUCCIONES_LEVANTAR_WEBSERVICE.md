# Instrucciones para Levantar el Webservice de Conciliación en Modo Local

## Requisitos Previos

1. **Java 8 o superior** instalado y configurado en el PATH
2. **Apache Tomcat 9.0.xx** descargado y extraído
3. **PostgreSQL** (opcional, para pruebas con base de datos local)

## Pasos para Levantar el Webservice

### 1. Descargar Apache Tomcat

1. Ve a: https://tomcat.apache.org/download-90.cgi
2. Descarga la versión "Windows Service Installer" o "32-bit/64-bit Windows Service Installer"
3. Extrae el contenido en la carpeta `apache-tomcat-9.0.xx` en el directorio raíz del proyecto

### 2. Compilar el Proyecto

Ejecuta el script de compilación:
```bash
compilar_webservice.bat
```

Este script:
- Compila todo el código fuente Java
- Copia los archivos de configuración
- Crea el archivo WAR (`ws-conciliacion.war`)

### 3. Levantar el Webservice

Ejecuta el script de despliegue:
```bash
levantar_webservice_local.bat
```

Este script:
- Configura Tomcat automáticamente
- Copia el archivo WAR al directorio webapps
- Crea un archivo de configuración para desarrollo local
- Inicia el servidor Tomcat

### 4. Verificar que el Webservice esté Funcionando

Una vez iniciado, el webservice estará disponible en:

- **URL del servicio**: http://localhost:8080/ws-conciliacion/services/WsConciliacion
- **WSDL**: http://localhost:8080/ws-conciliacion/services/WsConciliacion?wsdl
- **Administración de Axis2**: http://localhost:8080/axis2-admin/

### 5. Detener el Servidor

Para detener el servidor, ejecuta:
```bash
detener_webservice.bat
```

## Configuración de Base de Datos (Opcional)

Si quieres usar una base de datos local para pruebas:

1. Instala PostgreSQL
2. Crea las bases de datos `ingresos_dev` y `cajas_dev`
3. Modifica el archivo `config.properties` generado en:
   ```
   apache-tomcat-9.0.xx/webapps/ws-conciliacion/WEB-INF/classes/config.properties
   ```

## Operaciones Disponibles

El webservice expone las siguientes operaciones:

1. **generarConciliacion** - Genera archivos de conciliación
2. **reenviarCorreo** - Reenvía correos de conciliación
3. **obtenerUltimaFecha** - Obtiene la última fecha de conciliación
4. **consultarEstadoConciliacion** - Consulta el estado de una conciliación
5. **reenviarArchivoConnect** - Reenvía archivos a Connect

## Archivos de Log

Los logs se generan en la carpeta `logs/` del directorio raíz:
- `logArchivoConciliacionYYYYMMDD.log` - Logs de generación de archivos
- `logEstadoConciliacionYYYYMMDD.log` - Logs de consulta de estado
- `logCorreoConciliacionYYYYMMDD.log` - Logs de envío de correos
- `logFechaConciliacionYYYYMMDD.log` - Logs de consulta de fechas

## Solución de Problemas

### Error: "Java no está instalado"
- Instala Java 8 o superior
- Asegúrate de que esté en el PATH del sistema

### Error: "Tomcat no encontrado"
- Descarga Apache Tomcat 9.0.xx
- Extrae en la carpeta `apache-tomcat-9.0.xx`

### Error: "Puerto 8080 en uso"
- Cambia el puerto en `apache-tomcat-9.0.xx/conf/server.xml`
- O detén otros servicios que usen el puerto 8080

### Error de compilación
- Verifica que todas las librerías estén `portalconciliacion/Codigo Servicio/wsConciliacion/WebContent/WEB-INF/lib/`
- Asegúrate de usar Java 8 o superior

## Estructura del Proyecto

```
portalconciliacion/
├── Codigo Servicio/
│   └── wsConciliacion/
│       ├── src/                    # Código fuente Java
│       ├── WebContent/             # Contenido web
│       │   ├── WEB-INF/
│       │   │   ├── lib/           # Librerías
│       │   │   ├── services.xml   # Configuración de servicios
│       │   │   └── web.xml        # Configuración web
│       │   └── wsdl/              # Archivos WSDL
│       ├── resources/             # Archivos de configuración
│       └── pack/                  # Archivos compilados
└── scripts/                       # Scripts SQL
```

## Notas Importantes

- El webservice usa **Axis2 2.0.0** con **JAX-WS**
- Las conexiones a base de datos usan **PostgreSQL**
- Los logs se escriben en archivos con formato UTF-8
- El servicio requiere autenticación (ver clase `Utilidades.java`) 