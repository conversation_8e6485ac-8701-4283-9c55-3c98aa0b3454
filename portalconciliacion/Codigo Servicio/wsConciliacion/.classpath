<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="src"/>
	<classpathentry kind="src" path="resources"/>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-11">
		<attributes>
			<attribute name="module" value="true"/>
			<attribute name="owner.project.facets" value="java"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="con" path="org.eclipse.jst.j2ee.internal.web.container"/>
	<classpathentry kind="con" path="org.eclipse.jst.j2ee.internal.module.container"/>
	<classpathentry kind="con" path="org.eclipse.jst.server.core.container/org.eclipse.jst.server.tomcat.runtimeTarget/Apache Tomcat v9.0"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/axis2-kernel-2.0.0.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/axis2-transport-http-2.0.0.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/axis2-jaxws-2.0.0.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/axiom-api-1.4.0.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/axiom-impl-1.4.0.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/neethi-3.2.0.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/httpcore5-5.3.4.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/httpcore5-h2-5.3.4.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/httpclient5-5.5.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/slf4j-api-2.0.17.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/commons-discovery-0.2.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/commons-logging.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/gson-2.2.4.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/itext7-barcodes-7.1.19.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/itext7-font-asian-7.1.19.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/itext7-forms-7.1.19.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/itext7-hyph-7.1.19.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/itext7-io-7.1.19.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/itext7-kernel-7.1.19.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/itext7-layout-7.1.19.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/itext7-pdfa-7.1.19.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/itext7-sign-7.1.19.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/bcprov-jdk18on-1.79.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/bcpkix-jdk18on-1.79.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/postgresql-42.7.5.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/wsdl4j.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/jsch-0.1.54.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/jcifs-1.3.18.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/jakarta.mail-api-2.1.3.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/jakarta.activation-api-2.1.3.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/jakarta.jws-api-3.0.0.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/jakarta.xml.ws-api-4.0.2.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/jakarta.xml.bind-api-4.0.2.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/jakarta.xml.soap-api-3.0.2.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/saaj-impl-3.0.4.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/angus-mail-2.0.3.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/angus-activation-2.0.2.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/jaxws-rt-4.0.3.jar"/>
	<classpathentry kind="lib" path="WebContent/WEB-INF/lib/jaxb-runtime-4.0.5.jar"/>
	<classpathentry kind="output" path="WebContent/WEB-INF/classes"/>
</classpath>
