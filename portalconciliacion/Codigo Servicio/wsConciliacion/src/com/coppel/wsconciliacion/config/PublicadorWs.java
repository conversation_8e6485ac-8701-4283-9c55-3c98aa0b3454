package com.coppel.wsconciliacion.config;

import com.coppel.wsconciliacion.servicioweb.WsConciliacion;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;


import jakarta.xml.ws.Endpoint;

@WebListener
public class PublicadorWs implements ServletContextListener {

    private Endpoint endpoint;

    @Override
    public void contextInitialized(ServletContextEvent sce) {
        try {
            // Publica el servicio en esta URL relativa
            endpoint = Endpoint.publish("/ws/WsConciliacion", new WsConciliacion());
            System.out.println("✅ Servicio WsConciliacion publicado en /ws/WsConciliacion");
        } catch (Exception e) {
            System.err.println("❌ Error al publicar el servicio: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void contextDestroyed(ServletContextEvent sce) {
        if (endpoint != null) {
            endpoint.stop();
            System.out.println("🛑 Servicio detenido correctamente");
        }
    }
}
