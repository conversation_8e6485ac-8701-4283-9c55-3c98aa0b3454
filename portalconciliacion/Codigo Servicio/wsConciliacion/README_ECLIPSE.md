# Configuración de Eclipse para Webservice de Conciliación

## Requisitos Previos

1. **Eclipse IDE for Enterprise Java Developers** (versión 2023-12 o superior)
2. **Java 11 JDK** instalado y configurado
3. **Apache Tomcat 9.0.x** instalado

## Pasos para Importar el Proyecto

### 1. Abrir Eclipse
- Inicia Eclipse IDE
- Selecciona un workspace (directorio de trabajo)

### 2. Importar el Proyecto
1. **File → Import**
2. **General → Existing Projects into Workspace**
3. **Browse** y selecciona la carpeta: `portalconciliacion/Codigo Servicio/wsConciliacion`
4. **Finish**

### 3. Configurar el Servidor Tomcat
1. **Window → Show View → Servers**
2. **New → Server**
3. Selecciona **Apache → Tomcat v9.0 Server**
4. **Browse** y selecciona tu instalación de Tomcat
5. **Finish**

### 4. Agregar el Proyecto al Servidor
1. Click derecho en el servidor Tomcat
2. **Add and Remove**
3. Selecciona `wsConciliacion` y agrégalo
4. **Finish**

## Configuración del Proyecto

### Estructura del Proyecto
```
wsConciliacion/
├── src/                          # Código fuente Java
├── WebContent/                   # Contenido web
│   ├── WEB-INF/
│   │   ├── lib/                  # Librerías (ya incluidas)
│   │   ├── classes/              # Clases compiladas
│   │   ├── conf/
│   │   │   └── axis2.xml         # Configuración Axis2
│   │   ├── web.xml               # Configuración web
│   │   └── services.xml          # Configuración servicios
│   └── wsdl/                     # Archivos WSDL
└── resources/                    # Archivos de configuración
```

### Configuraciones Específicas

#### Java 11
- El proyecto está configurado para usar **Java 11**
- Compilador configurado para **source y target 11**
- Compatible con **Tomcat 9.0.x**

#### Axis2 Configuration
- **axis2.xml**: Configuración del motor Axis2
- **services.xml**: Definición del servicio WsConciliacion
- **web.xml**: Mapeo de servlets de Axis2

## Compilación y Despliegue

### 1. Compilar el Proyecto
- **Project → Build Project** (Ctrl+B)
- O **Project → Clean...** para limpiar y recompilar

### 2. Desplegar en Tomcat
- Click derecho en el proyecto
- **Run As → Run on Server**
- Selecciona tu servidor Tomcat
- **Finish**

### 3. Verificar el Despliegue
Una vez desplegado, el webservice estará disponible en:
- **URL del servicio**: http://localhost:8080/ws-conciliacion/services/WsConciliacion
- **WSDL**: http://localhost:8080/ws-conciliacion/services/WsConciliacion?wsdl

## Operaciones Disponibles

El webservice expone las siguientes operaciones:
1. **generarConciliacion**
2. **reenviarCorreo**
3. **obtenerUltimaFecha**
4. **consultarEstadoConciliacion**
5. **reenviarArchivoConnect**

## Debugging

### 1. Configurar Breakpoints
- Click en el margen izquierdo del editor para agregar breakpoints
- Los breakpoints se activarán cuando se ejecute el código

### 2. Debug del Webservice
1. Click derecho en el proyecto
2. **Debug As → Debug on Server**
3. Selecciona tu servidor Tomcat
4. **Finish**

### 3. Ver Logs
- **Window → Show View → Console** para ver logs de Tomcat
- **Window → Show View → Error Log** para ver errores de Eclipse

## Exportar WAR

### 1. Exportar desde Eclipse
1. Click derecho en el proyecto
2. **Export**
3. **Web → WAR file**
4. Selecciona destino y **Finish**

### 2. WAR Generado
El WAR incluirá:
- Todas las clases compiladas
- Configuración de Axis2
- Librerías necesarias
- Archivos de configuración

## Solución de Problemas

### Error: "Java version mismatch"
- Verifica que Eclipse use Java 11
- **Window → Preferences → Java → Installed JREs**

### Error: "Class not found"
- Verifica que todas las librerías estén en `WEB-INF/lib`
- **Project → Properties → Java Build Path → Libraries**

### Error: "Servlet not found"
- Verifica que `web.xml` esté correctamente configurado
- Revisa que las librerías de Axis2 estén presentes

### Error: "Port already in use"
- Cambia el puerto en la configuración de Tomcat
- O detén otros servicios que usen el puerto 8080

## Notas Importantes

- El proyecto usa **Axis2 2.0.0** con **JAX-WS**
- Compatible con **Java 11** y **Tomcat 9.0.x**
- Las conexiones a base de datos usan **PostgreSQL**
- Los logs se escriben en archivos con formato UTF-8 