# Documentación del Web Service de Conciliación

## Descripción General

Este es un **Web Service de Conciliación** desarrollado para Coppel que maneja la conciliación de datos financieros entre diferentes sistemas. El servicio está construido con **Java** y utiliza **Apache Axis 2** como framework de servicios web.

## Funcionalidad Principal

El web service se encarga de:

1. **Generar archivos de conciliación** entre sistemas de ingresos y cajas
2. **Enviar archivos por correo electrónico** a destinatarios configurados
3. **Transferir archivos a sistemas externos** mediante SFTP/Connect Direct
4. **Consultar estados** de procesos de conciliación
5. **Obtener fechas** de última conciliación
6. **Reenviar archivos** en caso de errores

## Tecnologías Utilizadas

### Backend (Java)
- **Java 11** (migrado desde Java 8)
- **Apache Axis 2.0.0** - Framework de servicios web
- **Jakarta EE** - APIs modernas (Jakarta Mail, Jakarta Activation, Jakarta JWS)
- **PostgreSQL** - Base de datos
- **iText 7** - Generación de PDFs
- **JSch** - Cliente SFTP
- **JCIFS** - Acceso a recursos de red (SMB)
- **Apache HttpClient 5** - Cliente HTTP
- **Gson** - Procesamiento JSON

### Frontend (PHP)
- **PHP** - Interfaz web
- **jQuery** - JavaScript
- **HTML/CSS** - Interfaz de usuario

### Infraestructura
- **Apache Tomcat** - Servidor de aplicaciones
- **PostgreSQL** - Base de datos
- **SFTP/Connect Direct** - Transferencia de archivos

## Operaciones del Web Service

### 1. generarConciliacion()
- **Descripción**: Inicia el proceso de conciliación de datos
- **Parámetros**: Ninguno
- **Retorna**: String con el estado de la operación
- **Formato respuesta**: "1|Mensaje de éxito" o "2|Mensaje de error"

### 2. obtenerUltimaFecha()
- **Descripción**: Obtiene la fecha de la última conciliación realizada
- **Parámetros**: Ninguno
- **Retorna**: String con la fecha en formato específico

### 3. reenviarCorreo(int peticion)
- **Descripción**: Reenvía correos o elimina archivos según el parámetro
- **Parámetros**: 
  - `peticion`: 1 = reenviar correo, otro valor = eliminar archivos
- **Retorna**: String con el estado de la operación

### 4. consultarEstadoConciliacion()
- **Descripción**: Consulta el estado actual del proceso de conciliación
- **Parámetros**: Ninguno
- **Retorna**: String con el estado actual

### 5. reenviarArchivoConnect(int peticion)
- **Descripción**: Reenvía archivos al sistema Connect Direct o elimina archivos
- **Parámetros**:
  - `peticion`: 1 = reenviar archivo, otro valor = eliminar archivos
- **Retorna**: String con el estado de la operación

## Configuración

El servicio utiliza un archivo `config.properties` con las siguientes configuraciones:

```properties
# Bases de datos
servidorIngresos=*************:5432
baseIngresos=ingresos
usuarioIngresos=sysingresos
contrasenaIngresos=1299e3097ebcb90b651b4510559c63a7

servidorCajas=************:5432
baseCajas=cajas
usuarioCajas=syscajas
contrasenaCajas=270ba1e1661d87e93ee5235555510704

# Rutas de logs
rutaConciliacionLog=/sysx/progs/logs/conciliacion/logArchivoConciliacion
rutaConsultarEstadoLog=/sysx/progs/logs/conciliacion/logEstadoConciliacion
rutaCorreoLog=/sysx/progs/logs/conciliacion/logCorreoConciliacion
rutaFechaLog=/sysx/progs/logs/conciliacion/logFechaConciliacion
rutaEstatus=/sysx/progs/estatus/conciliacion/estatusConciliacion.txt
```

## Cómo Probar el Web Service

### 1. Despliegue en Tomcat

1. **Compilar el proyecto**:
   ```bash
   # El proyecto ya incluye el WAR compilado
   # Ubicación: portalconciliacion/Codigo Servicio/wsConciliacion/pack/ws-conciliacion.war
   ```

2. **Desplegar en Tomcat**:
   - Copiar `ws-conciliacion.war` a `$TOMCAT_HOME/webapps/`
   - Reiniciar Tomcat
   - El servicio estará disponible en: `http://localhost:8080/ws-conciliacion/`

### 2. URLs del Servicio

- **WSDL**: `http://localhost:8080/ws-conciliacion/services/WsConciliacion?wsdl`
- **Endpoint**: `http://localhost:8080/ws-conciliacion/services/WsConciliacion`

### 3. Ejemplos de Peticiones SOAP

#### Generar Conciliación
```xml
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:impl="http://servicioweb.wsconciliacion.coppel.com">
   <soapenv:Header/>
   <soapenv:Body>
      <impl:generarConciliacion/>
   </soapenv:Body>
</soapenv:Envelope>
```

#### Obtener Última Fecha
```xml
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:impl="http://servicioweb.wsconciliacion.coppel.com">
   <soapenv:Header/>
   <soapenv:Body>
      <impl:obtenerUltimaFecha/>
   </soapenv:Body>
</soapenv:Envelope>
```

#### Reenviar Correo
```xml
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:impl="http://servicioweb.wsconciliacion.coppel.com">
   <soapenv:Header/>
   <soapenv:Body>
      <impl:reenviarCorreo>
         <impl:peticion>1</impl:peticion>
      </impl:reenviarCorreo>
   </soapenv:Body>
</soapenv:Envelope>
```

#### Consultar Estado
```xml
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:impl="http://servicioweb.wsconciliacion.coppel.com">
   <soapenv:Header/>
   <soapenv:Body>
      <impl:consultarEstadoConciliacion/>
   </soapenv:Body>
</soapenv:Envelope>
```

#### Reenviar Archivo Connect
```xml
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:impl="http://servicioweb.wsconciliacion.coppel.com">
   <soapenv:Header/>
   <soapenv:Body>
      <impl:reenviarArchivoConnect>
         <impl:peticion>1</impl:peticion>
      </impl:reenviarArchivoConnect>
   </soapenv:Body>
</soapenv:Envelope>
```

### 4. Herramientas para Probar

#### Usando cURL
```bash
# Generar conciliación
curl -X POST http://localhost:8080/ws-conciliacion/services/WsConciliacion \
  -H "Content-Type: text/xml; charset=utf-8" \
  -H "SOAPAction: generarConciliacion" \
  -d @generar_conciliacion.xml

# Consultar estado
curl -X POST http://localhost:8080/ws-conciliacion/services/WsConciliacion \
  -H "Content-Type: text/xml; charset=utf-8" \
  -H "SOAPAction: consultarEstadoConciliacion" \
  -d @consultar_estado.xml
```

#### Usando SoapUI
1. Crear nuevo proyecto SOAP
2. Importar WSDL: `http://localhost:8080/ws-conciliacion/services/WsConciliacion?wsdl`
3. Crear requests para cada operación
4. Ejecutar las peticiones

#### Usando Postman
1. Crear nueva petición POST
2. URL: `http://localhost:8080/ws-conciliacion/services/WsConciliacion`
3. Headers:
   - `Content-Type: text/xml; charset=utf-8`
   - `SOAPAction: [nombre_operacion]`
4. Body: XML con la petición SOAP

### 5. Interfaz Web

El proyecto incluye una interfaz web en PHP ubicada en `portalconciliacion/Codigo Pagina/` que permite:

- Generar conciliaciones
- Consultar estados
- Reenviar correos
- Reenviar archivos

Para usar la interfaz web:
1. Configurar servidor web (Apache/Nginx) con PHP
2. Copiar archivos de `Codigo Pagina/` al directorio web
3. Configurar conexiones de base de datos
4. Acceder vía navegador

## Estructura del Proyecto

```
portalconciliacion/
├── Codigo Servicio/wsConciliacion/
│   ├── src/com/coppel/wsconciliacion/
│   │   ├── servicioweb/WsConciliacion.java    # Clase principal del WS
│   │   ├── logica/ConciliacionDeDatos.java    # Lógica de negocio
│   │   ├── accesodatos/AccesoDatos.java       # Acceso a datos
│   │   ├── conexion/ConexionConciliacion.java # Conexiones
│   │   └── entidades/                         # Entidades de datos
│   ├── WebContent/
│   │   ├── WEB-INF/
│   │   │   ├── services.xml                   # Configuración del servicio
│   │   │   ├── lib/                          # Librerías JAR
│   │   │   └── web.xml                       # Configuración web
│   │   └── wsdl/WsConciliacion.wsdl          # Definición WSDL
│   └── resources/config.properties           # Configuración
└── Codigo Pagina/                            # Interfaz web PHP
    ├── index.php
    ├── ajax/
    ├── files/
    └── scripts/
```

## Logs y Monitoreo

El servicio genera logs en las siguientes ubicaciones:
- `/sysx/progs/logs/conciliacion/logArchivoConciliacion`
- `/sysx/progs/logs/conciliacion/logEstadoConciliacion`
- `/sysx/progs/logs/conciliacion/logCorreoConciliacion`
- `/sysx/progs/logs/conciliacion/logFechaConciliacion`
- `/sysx/progs/estatus/conciliacion/estatusConciliacion.txt`

## Consideraciones de Seguridad

1. **Credenciales**: Las contraseñas están encriptadas en el archivo de configuración
2. **Logs**: El servicio registra todas las operaciones para auditoría
3. **Validaciones**: Se realizan validaciones de datos antes de procesar
4. **Manejo de errores**: Implementado manejo robusto de excepciones

## Migración a Java 11

El proyecto ha sido migrado de Java 8 a Java 11, incluyendo:
- Actualización de librerías Jakarta EE
- Migración de javax.* a jakarta.*
- Actualización de dependencias compatibles
- Scripts de migración automatizados

## Troubleshooting

### Problemas Comunes

1. **Error de conexión a base de datos**:
   - Verificar configuración en `config.properties`
   - Comprobar conectividad de red
   - Validar credenciales

2. **Error de permisos en archivos**:
   - Verificar permisos en directorios de logs
   - Comprobar permisos de escritura

3. **Error de SFTP**:
   - Validar configuración de SFTP
   - Comprobar conectividad al servidor remoto
   - Verificar credenciales SFTP

4. **Error de correo**:
   - Verificar configuración del servidor SMTP
   - Comprobar credenciales de correo
   - Validar direcciones de correo

### Logs de Error

Los errores se registran en los archivos de log correspondientes con el formato:
```
[FECHA] [PROCESO] [MENSAJE]
```

## Contacto y Soporte

Para soporte técnico o consultas sobre el web service, contactar al equipo de desarrollo de Coppel. 