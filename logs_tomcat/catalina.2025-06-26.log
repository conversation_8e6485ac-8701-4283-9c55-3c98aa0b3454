26-Jun-2025 02:10:02.332 WARNING [main] org.apache.tomcat.util.digester.SetPropertiesRule.begin Match [Server/Service/Connector] failed to set property [sslVerifyClient] to [optional]
26-Jun-2025 02:10:02.335 WARNING [main] org.apache.tomcat.util.net.SSLHostConfig.setProtocols The protocol [TLSv1.1] was added to the list of protocols on the SSLHostConfig named [_default_]. Check if a +/- prefix is missing.
26-Jun-2025 02:10:02.336 WARNING [main] org.apache.tomcat.util.net.SSLHostConfig.setProtocols The protocol [SSLv2Hello] was added to the list of protocols on the SSLHostConfig named [_default_]. Check if a +/- prefix is missing.
26-Jun-2025 02:10:02.370 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.96
26-Jun-2025 02:10:02.370 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Oct 3 2024 19:44:30 UTC
26-Jun-2025 02:10:02.370 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: ********
26-Jun-2025 02:10:02.370 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Linux
26-Jun-2025 02:10:02.370 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            4.18.0-553.5.1.el8_10.x86_64
26-Jun-2025 02:10:02.371 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
26-Jun-2025 02:10:02.371 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             /usr/lib/jvm/java-1.8.0-openjdk-1.8.0.452.b09-2.el8.x86_64/jre
26-Jun-2025 02:10:02.371 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           1.8.0_452-b09
26-Jun-2025 02:10:02.371 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            Red Hat, Inc.
26-Jun-2025 02:10:02.371 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         /sysx/apache-tomcat-9.0.96
26-Jun-2025 02:10:02.371 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         /sysx/apache-tomcat-9.0.96
26-Jun-2025 02:10:02.371 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=/usr/local/tomcat/conf/logging.properties
26-Jun-2025 02:10:02.372 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
26-Jun-2025 02:10:02.372 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
26-Jun-2025 02:10:02.372 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
26-Jun-2025 02:10:02.372 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
26-Jun-2025 02:10:02.372 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dignore.endorsed.dirs=
26-Jun-2025 02:10:02.372 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=/usr/local/tomcat
26-Jun-2025 02:10:02.372 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=/usr/local/tomcat
26-Jun-2025 02:10:02.372 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=/usr/local/tomcat/temp
26-Jun-2025 02:10:02.374 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent The Apache Tomcat Native library which allows using OpenSSL was not found on the java.library.path: [/usr/java/packages/lib/amd64:/usr/lib64:/lib64:/lib:/usr/lib]
26-Jun-2025 02:10:02.652 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["http-nio-8080"]
26-Jun-2025 02:10:02.664 SEVERE [main] org.apache.catalina.util.LifecycleBase.handleSubClassException Failed to initialize component [Connector["http-nio-8080"]]
	org.apache.catalina.LifecycleException: Protocol handler initialization failed
		at org.apache.catalina.connector.Connector.initInternal(Connector.java:1027)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:122)
		at org.apache.catalina.core.StandardService.initInternal(StandardService.java:525)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:122)
		at org.apache.catalina.core.StandardServer.initInternal(StandardServer.java:986)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:122)
		at org.apache.catalina.startup.Catalina.load(Catalina.java:686)
		at org.apache.catalina.startup.Catalina.load(Catalina.java:709)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.load(Bootstrap.java:302)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:472)
	Caused by: java.net.BindException: Address already in use
		at sun.nio.ch.Net.bind0(Native Method)
		at sun.nio.ch.Net.bind(Net.java:461)
		at sun.nio.ch.Net.bind(Net.java:453)
		at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:222)
		at org.apache.tomcat.util.net.NioEndpoint.initServerSocket(NioEndpoint.java:268)
		at org.apache.tomcat.util.net.NioEndpoint.bind(NioEndpoint.java:223)
		at org.apache.tomcat.util.net.AbstractEndpoint.bindWithCleanup(AbstractEndpoint.java:1373)
		at org.apache.tomcat.util.net.AbstractEndpoint.init(AbstractEndpoint.java:1386)
		at org.apache.coyote.AbstractProtocol.init(AbstractProtocol.java:663)
		at org.apache.coyote.http11.AbstractHttp11Protocol.init(AbstractHttp11Protocol.java:77)
		at org.apache.catalina.connector.Connector.initInternal(Connector.java:1025)
		... 13 more
26-Jun-2025 02:10:02.665 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler ["https-jsse-nio-8443"]
26-Jun-2025 02:10:02.803 INFO [main] org.apache.tomcat.util.net.AbstractEndpoint.logCertificate Connector [https-jsse-nio-8443], TLS virtual host [_default_], certificate type [UNDEFINED] configured from keystore [/usr/local/tomcat/conf/tomcat.keystore] using alias [tomcat] with trust store [null]
26-Jun-2025 02:10:02.817 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [628] milliseconds
26-Jun-2025 02:10:02.854 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
26-Jun-2025 02:10:02.854 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.96]
26-Jun-2025 02:10:02.858 SEVERE [main] org.apache.catalina.startup.HostConfig.beforeStart Unable to create directory for deployment: [/sysx/apache-tomcat-9.0.96/conf/Catalina/localhost]
26-Jun-2025 02:10:02.874 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive [/sysx/apache-tomcat-9.0.96/webapps/ws-conciliacion_ant.war]
26-Jun-2025 02:10:02.896 SEVERE [main] org.apache.catalina.startup.ContextConfig.beforeStart Exception fixing docBase for context [/ws-conciliacion_ant]
	java.io.FileNotFoundException: /sysx/apache-tomcat-9.0.96/webapps/ws-conciliacion_ant.war (Permission denied)
		at java.io.FileInputStream.open0(Native Method)
		at java.io.FileInputStream.open(FileInputStream.java:195)
		at java.io.FileInputStream.<init>(FileInputStream.java:138)
		at java.io.FileInputStream.<init>(FileInputStream.java:93)
		at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90)
		at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188)
		at org.apache.catalina.startup.ExpandWar.expand(ExpandWar.java:86)
		at org.apache.catalina.startup.ContextConfig.fixDocBase(ContextConfig.java:801)
		at org.apache.catalina.startup.ContextConfig.beforeStart(ContextConfig.java:937)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:292)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:385)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:163)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployWAR(HostConfig.java:1014)
		at org.apache.catalina.startup.HostConfig$DeployWar.run(HostConfig.java:1866)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployWARs(HostConfig.java:816)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:468)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1584)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:385)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:332)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:415)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:735)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:473)
26-Jun-2025 02:10:02.897 WARNING [main] org.apache.catalina.core.StandardContext.postWorkDirectory Failed to create work directory [/sysx/apache-tomcat-9.0.96/work/Catalina/localhost/ws-conciliacion_ant] for context [/ws-conciliacion_ant]
26-Jun-2025 02:10:02.913 SEVERE [main] org.apache.catalina.startup.HostConfig.deployWAR Error deploying web application archive [/sysx/apache-tomcat-9.0.96/webapps/ws-conciliacion_ant.war]
	java.lang.IllegalStateException: Error starting child
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:602)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployWAR(HostConfig.java:1014)
		at org.apache.catalina.startup.HostConfig$DeployWar.run(HostConfig.java:1866)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployWARs(HostConfig.java:816)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:468)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1584)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:385)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:332)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:415)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:735)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:473)
	Caused by: org.apache.catalina.LifecycleException: Failed to initialize component [org.apache.catalina.webresources.WarResourceSet@23529fee]
		at org.apache.catalina.util.LifecycleBase.handleSubClassException(LifecycleBase.java:402)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:125)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:155)
		at org.apache.catalina.webresources.StandardRoot.startInternal(StandardRoot.java:715)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardContext.resourcesStart(StandardContext.java:4118)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4240)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		... 37 more
	Caused by: java.lang.IllegalArgumentException: java.io.FileNotFoundException: /sysx/apache-tomcat-9.0.96/webapps/ws-conciliacion_ant.war (Permission denied)
		at org.apache.catalina.webresources.AbstractSingleArchiveResourceSet.initInternal(AbstractSingleArchiveResourceSet.java:141)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:122)
		... 44 more
	Caused by: java.io.FileNotFoundException: /sysx/apache-tomcat-9.0.96/webapps/ws-conciliacion_ant.war (Permission denied)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:231)
		at java.util.zip.ZipFile.<init>(ZipFile.java:157)
		at java.util.jar.JarFile.<init>(JarFile.java:169)
		at java.util.jar.JarFile.<init>(JarFile.java:133)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:294)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:279)
		at org.apache.catalina.webresources.AbstractSingleArchiveResourceSet.initInternal(AbstractSingleArchiveResourceSet.java:138)
		... 45 more
26-Jun-2025 02:10:02.915 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive [/sysx/apache-tomcat-9.0.96/webapps/ws-conciliacion_ant.war] has finished in [41] ms
26-Jun-2025 02:10:02.916 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive [/sysx/apache-tomcat-9.0.96/webapps/ws-conciliacion_mich.war]
26-Jun-2025 02:10:02.920 WARNING [main] org.apache.catalina.core.StandardContext.postWorkDirectory Failed to create work directory [/sysx/apache-tomcat-9.0.96/work/Catalina/localhost/ws-conciliacion_mich] for context [/ws-conciliacion_mich]
26-Jun-2025 02:10:03.090 SEVERE [main] org.apache.jasper.EmbeddedServletOptions.<init> The scratchDir you specified: [/sysx/apache-tomcat-9.0.96/work/Catalina/localhost/ws-conciliacion_mich] is unusable.
26-Jun-2025 02:10:03.112 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive [/sysx/apache-tomcat-9.0.96/webapps/ws-conciliacion_mich.war] has finished in [195] ms
26-Jun-2025 02:10:03.112 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive [/sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40.war]
26-Jun-2025 02:10:03.114 SEVERE [main] org.apache.catalina.startup.ContextConfig.beforeStart Exception fixing docBase for context [/FacturacionElectronicaCFDI40]
	java.io.FileNotFoundException: /sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40.war (Permission denied)
		at java.io.FileInputStream.open0(Native Method)
		at java.io.FileInputStream.open(FileInputStream.java:195)
		at java.io.FileInputStream.<init>(FileInputStream.java:138)
		at java.io.FileInputStream.<init>(FileInputStream.java:93)
		at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90)
		at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188)
		at org.apache.catalina.startup.ExpandWar.expand(ExpandWar.java:86)
		at org.apache.catalina.startup.ContextConfig.fixDocBase(ContextConfig.java:801)
		at org.apache.catalina.startup.ContextConfig.beforeStart(ContextConfig.java:937)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:292)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:385)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:163)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployWAR(HostConfig.java:1014)
		at org.apache.catalina.startup.HostConfig$DeployWar.run(HostConfig.java:1866)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployWARs(HostConfig.java:816)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:468)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1584)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:385)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:332)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:415)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:735)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:473)
26-Jun-2025 02:10:03.115 WARNING [main] org.apache.catalina.core.StandardContext.postWorkDirectory Failed to create work directory [/sysx/apache-tomcat-9.0.96/work/Catalina/localhost/FacturacionElectronicaCFDI40] for context [/FacturacionElectronicaCFDI40]
26-Jun-2025 02:10:03.115 SEVERE [main] org.apache.catalina.startup.HostConfig.deployWAR Error deploying web application archive [/sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40.war]
	java.lang.IllegalStateException: Error starting child
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:602)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployWAR(HostConfig.java:1014)
		at org.apache.catalina.startup.HostConfig$DeployWar.run(HostConfig.java:1866)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployWARs(HostConfig.java:816)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:468)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1584)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:385)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:332)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:415)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:735)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:473)
	Caused by: org.apache.catalina.LifecycleException: Failed to initialize component [org.apache.catalina.webresources.WarResourceSet@6150c3ec]
		at org.apache.catalina.util.LifecycleBase.handleSubClassException(LifecycleBase.java:402)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:125)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:155)
		at org.apache.catalina.webresources.StandardRoot.startInternal(StandardRoot.java:715)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardContext.resourcesStart(StandardContext.java:4118)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4240)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		... 37 more
	Caused by: java.lang.IllegalArgumentException: java.io.FileNotFoundException: /sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40.war (Permission denied)
		at org.apache.catalina.webresources.AbstractSingleArchiveResourceSet.initInternal(AbstractSingleArchiveResourceSet.java:141)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:122)
		... 44 more
	Caused by: java.io.FileNotFoundException: /sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40.war (Permission denied)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:231)
		at java.util.zip.ZipFile.<init>(ZipFile.java:157)
		at java.util.jar.JarFile.<init>(JarFile.java:169)
		at java.util.jar.JarFile.<init>(JarFile.java:133)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:294)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:279)
		at org.apache.catalina.webresources.AbstractSingleArchiveResourceSet.initInternal(AbstractSingleArchiveResourceSet.java:138)
		... 45 more
26-Jun-2025 02:10:03.116 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive [/sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40.war] has finished in [4] ms
26-Jun-2025 02:10:03.117 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive [/sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40_1.war]
26-Jun-2025 02:10:03.118 SEVERE [main] org.apache.catalina.startup.ContextConfig.beforeStart Exception fixing docBase for context [/FacturacionElectronicaCFDI40_1]
	java.io.FileNotFoundException: /sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40_1.war (Permission denied)
		at java.io.FileInputStream.open0(Native Method)
		at java.io.FileInputStream.open(FileInputStream.java:195)
		at java.io.FileInputStream.<init>(FileInputStream.java:138)
		at java.io.FileInputStream.<init>(FileInputStream.java:93)
		at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90)
		at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188)
		at org.apache.catalina.startup.ExpandWar.expand(ExpandWar.java:86)
		at org.apache.catalina.startup.ContextConfig.fixDocBase(ContextConfig.java:801)
		at org.apache.catalina.startup.ContextConfig.beforeStart(ContextConfig.java:937)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:292)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:385)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:163)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployWAR(HostConfig.java:1014)
		at org.apache.catalina.startup.HostConfig$DeployWar.run(HostConfig.java:1866)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployWARs(HostConfig.java:816)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:468)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1584)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:385)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:332)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:415)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:735)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:473)
26-Jun-2025 02:10:03.119 WARNING [main] org.apache.catalina.core.StandardContext.postWorkDirectory Failed to create work directory [/sysx/apache-tomcat-9.0.96/work/Catalina/localhost/FacturacionElectronicaCFDI40_1] for context [/FacturacionElectronicaCFDI40_1]
26-Jun-2025 02:10:03.120 SEVERE [main] org.apache.catalina.startup.HostConfig.deployWAR Error deploying web application archive [/sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40_1.war]
	java.lang.IllegalStateException: Error starting child
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:602)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployWAR(HostConfig.java:1014)
		at org.apache.catalina.startup.HostConfig$DeployWar.run(HostConfig.java:1866)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployWARs(HostConfig.java:816)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:468)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1584)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:385)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:332)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:415)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:735)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:473)
	Caused by: org.apache.catalina.LifecycleException: Failed to initialize component [org.apache.catalina.webresources.WarResourceSet@3fc2959f]
		at org.apache.catalina.util.LifecycleBase.handleSubClassException(LifecycleBase.java:402)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:125)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:155)
		at org.apache.catalina.webresources.StandardRoot.startInternal(StandardRoot.java:715)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardContext.resourcesStart(StandardContext.java:4118)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4240)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		... 37 more
	Caused by: java.lang.IllegalArgumentException: java.io.FileNotFoundException: /sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40_1.war (Permission denied)
		at org.apache.catalina.webresources.AbstractSingleArchiveResourceSet.initInternal(AbstractSingleArchiveResourceSet.java:141)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:122)
		... 44 more
	Caused by: java.io.FileNotFoundException: /sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40_1.war (Permission denied)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:231)
		at java.util.zip.ZipFile.<init>(ZipFile.java:157)
		at java.util.jar.JarFile.<init>(JarFile.java:169)
		at java.util.jar.JarFile.<init>(JarFile.java:133)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:294)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:279)
		at org.apache.catalina.webresources.AbstractSingleArchiveResourceSet.initInternal(AbstractSingleArchiveResourceSet.java:138)
		... 45 more
26-Jun-2025 02:10:03.121 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive [/sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40_1.war] has finished in [4] ms
26-Jun-2025 02:10:03.121 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive [/sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40_Dev.war]
26-Jun-2025 02:10:03.123 SEVERE [main] org.apache.catalina.startup.ContextConfig.beforeStart Exception fixing docBase for context [/FacturacionElectronicaCFDI40_Dev]
	java.io.FileNotFoundException: /sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40_Dev.war (Permission denied)
		at java.io.FileInputStream.open0(Native Method)
		at java.io.FileInputStream.open(FileInputStream.java:195)
		at java.io.FileInputStream.<init>(FileInputStream.java:138)
		at java.io.FileInputStream.<init>(FileInputStream.java:93)
		at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90)
		at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188)
		at org.apache.catalina.startup.ExpandWar.expand(ExpandWar.java:86)
		at org.apache.catalina.startup.ContextConfig.fixDocBase(ContextConfig.java:801)
		at org.apache.catalina.startup.ContextConfig.beforeStart(ContextConfig.java:937)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:292)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:385)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:163)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployWAR(HostConfig.java:1014)
		at org.apache.catalina.startup.HostConfig$DeployWar.run(HostConfig.java:1866)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployWARs(HostConfig.java:816)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:468)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1584)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:385)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:332)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:415)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:735)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:473)
26-Jun-2025 02:10:03.124 WARNING [main] org.apache.catalina.core.StandardContext.postWorkDirectory Failed to create work directory [/sysx/apache-tomcat-9.0.96/work/Catalina/localhost/FacturacionElectronicaCFDI40_Dev] for context [/FacturacionElectronicaCFDI40_Dev]
26-Jun-2025 02:10:03.124 SEVERE [main] org.apache.catalina.startup.HostConfig.deployWAR Error deploying web application archive [/sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40_Dev.war]
	java.lang.IllegalStateException: Error starting child
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:602)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployWAR(HostConfig.java:1014)
		at org.apache.catalina.startup.HostConfig$DeployWar.run(HostConfig.java:1866)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployWARs(HostConfig.java:816)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:468)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1584)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:385)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:332)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:415)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:735)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:473)
	Caused by: org.apache.catalina.LifecycleException: Failed to initialize component [org.apache.catalina.webresources.WarResourceSet@229f66ed]
		at org.apache.catalina.util.LifecycleBase.handleSubClassException(LifecycleBase.java:402)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:125)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:155)
		at org.apache.catalina.webresources.StandardRoot.startInternal(StandardRoot.java:715)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardContext.resourcesStart(StandardContext.java:4118)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4240)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		... 37 more
	Caused by: java.lang.IllegalArgumentException: java.io.FileNotFoundException: /sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40_Dev.war (Permission denied)
		at org.apache.catalina.webresources.AbstractSingleArchiveResourceSet.initInternal(AbstractSingleArchiveResourceSet.java:141)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:122)
		... 44 more
	Caused by: java.io.FileNotFoundException: /sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40_Dev.war (Permission denied)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:231)
		at java.util.zip.ZipFile.<init>(ZipFile.java:157)
		at java.util.jar.JarFile.<init>(JarFile.java:169)
		at java.util.jar.JarFile.<init>(JarFile.java:133)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:294)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:279)
		at org.apache.catalina.webresources.AbstractSingleArchiveResourceSet.initInternal(AbstractSingleArchiveResourceSet.java:138)
		... 45 more
26-Jun-2025 02:10:03.125 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive [/sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40_Dev.war] has finished in [4] ms
26-Jun-2025 02:10:03.125 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive [/sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40_logo.war]
26-Jun-2025 02:10:03.127 SEVERE [main] org.apache.catalina.startup.ContextConfig.beforeStart Exception fixing docBase for context [/FacturacionElectronicaCFDI40_logo]
	java.io.FileNotFoundException: /sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40_logo.war (Permission denied)
		at java.io.FileInputStream.open0(Native Method)
		at java.io.FileInputStream.open(FileInputStream.java:195)
		at java.io.FileInputStream.<init>(FileInputStream.java:138)
		at java.io.FileInputStream.<init>(FileInputStream.java:93)
		at sun.net.www.protocol.file.FileURLConnection.connect(FileURLConnection.java:90)
		at sun.net.www.protocol.file.FileURLConnection.getInputStream(FileURLConnection.java:188)
		at org.apache.catalina.startup.ExpandWar.expand(ExpandWar.java:86)
		at org.apache.catalina.startup.ContextConfig.fixDocBase(ContextConfig.java:801)
		at org.apache.catalina.startup.ContextConfig.beforeStart(ContextConfig.java:937)
		at org.apache.catalina.startup.ContextConfig.lifecycleEvent(ContextConfig.java:292)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:385)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:163)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployWAR(HostConfig.java:1014)
		at org.apache.catalina.startup.HostConfig$DeployWar.run(HostConfig.java:1866)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployWARs(HostConfig.java:816)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:468)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1584)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:385)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:332)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:415)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:735)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:473)
26-Jun-2025 02:10:03.128 WARNING [main] org.apache.catalina.core.StandardContext.postWorkDirectory Failed to create work directory [/sysx/apache-tomcat-9.0.96/work/Catalina/localhost/FacturacionElectronicaCFDI40_logo] for context [/FacturacionElectronicaCFDI40_logo]
26-Jun-2025 02:10:03.129 SEVERE [main] org.apache.catalina.startup.HostConfig.deployWAR Error deploying web application archive [/sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40_logo.war]
	java.lang.IllegalStateException: Error starting child
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:602)
		at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:571)
		at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:603)
		at org.apache.catalina.startup.HostConfig.deployWAR(HostConfig.java:1014)
		at org.apache.catalina.startup.HostConfig$DeployWar.run(HostConfig.java:1866)
		at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:112)
		at org.apache.catalina.startup.HostConfig.deployWARs(HostConfig.java:816)
		at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:468)
		at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1584)
		at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:312)
		at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:109)
		at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:385)
		at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:332)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:776)
		at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:721)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1203)
		at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1193)
		at java.util.concurrent.FutureTask.run(FutureTask.java:266)
		at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
		at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
		at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:749)
		at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:211)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardService.startInternal(StandardService.java:415)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:874)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:735)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:473)
	Caused by: org.apache.catalina.LifecycleException: Failed to initialize component [org.apache.catalina.webresources.WarResourceSet@f5958c9]
		at org.apache.catalina.util.LifecycleBase.handleSubClassException(LifecycleBase.java:402)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:125)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:155)
		at org.apache.catalina.webresources.StandardRoot.startInternal(StandardRoot.java:715)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.StandardContext.resourcesStart(StandardContext.java:4118)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4240)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:599)
		... 37 more
	Caused by: java.lang.IllegalArgumentException: java.io.FileNotFoundException: /sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40_logo.war (Permission denied)
		at org.apache.catalina.webresources.AbstractSingleArchiveResourceSet.initInternal(AbstractSingleArchiveResourceSet.java:141)
		at org.apache.catalina.util.LifecycleBase.init(LifecycleBase.java:122)
		... 44 more
	Caused by: java.io.FileNotFoundException: /sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40_logo.war (Permission denied)
		at java.util.zip.ZipFile.open(Native Method)
		at java.util.zip.ZipFile.<init>(ZipFile.java:231)
		at java.util.zip.ZipFile.<init>(ZipFile.java:157)
		at java.util.jar.JarFile.<init>(JarFile.java:169)
		at java.util.jar.JarFile.<init>(JarFile.java:133)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:294)
		at org.apache.tomcat.util.compat.JreCompat.jarFileNewInstance(JreCompat.java:279)
		at org.apache.catalina.webresources.AbstractSingleArchiveResourceSet.initInternal(AbstractSingleArchiveResourceSet.java:138)
		... 45 more
26-Jun-2025 02:10:03.130 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive [/sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40_logo.war] has finished in [4] ms
26-Jun-2025 02:10:03.131 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive [/sysx/apache-tomcat-9.0.96/webapps/wsConciliacion.war]
26-Jun-2025 02:10:03.134 WARNING [main] org.apache.catalina.core.StandardContext.postWorkDirectory Failed to create work directory [/sysx/apache-tomcat-9.0.96/work/Catalina/localhost/wsConciliacion] for context [/wsConciliacion]
26-Jun-2025 02:10:03.143 SEVERE [main] org.apache.jasper.EmbeddedServletOptions.<init> The scratchDir you specified: [/sysx/apache-tomcat-9.0.96/work/Catalina/localhost/wsConciliacion] is unusable.
26-Jun-2025 02:10:03.144 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive [/sysx/apache-tomcat-9.0.96/webapps/wsConciliacion.war] has finished in [13] ms
26-Jun-2025 02:10:03.146 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive [/sysx/apache-tomcat-9.0.96/webapps/ws-conciliacion.war]
26-Jun-2025 02:10:03.148 WARNING [main] org.apache.catalina.core.StandardContext.postWorkDirectory Failed to create work directory [/sysx/apache-tomcat-9.0.96/work/Catalina/localhost/ws-conciliacion] for context [/ws-conciliacion]
26-Jun-2025 02:10:03.158 SEVERE [main] org.apache.jasper.EmbeddedServletOptions.<init> The scratchDir you specified: [/sysx/apache-tomcat-9.0.96/work/Catalina/localhost/ws-conciliacion] is unusable.
26-Jun-2025 02:10:03.158 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive [/sysx/apache-tomcat-9.0.96/webapps/ws-conciliacion.war] has finished in [12] ms
26-Jun-2025 02:10:03.159 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [/sysx/apache-tomcat-9.0.96/webapps/ROOT]
26-Jun-2025 02:10:03.160 WARNING [main] org.apache.catalina.core.StandardContext.postWorkDirectory Failed to create work directory [/sysx/apache-tomcat-9.0.96/work/Catalina/localhost/ROOT] for context []
26-Jun-2025 02:10:03.171 SEVERE [main] org.apache.jasper.EmbeddedServletOptions.<init> The scratchDir you specified: [/sysx/apache-tomcat-9.0.96/work/Catalina/localhost/ROOT] is unusable.
26-Jun-2025 02:10:03.172 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [/sysx/apache-tomcat-9.0.96/webapps/ROOT] has finished in [13] ms
26-Jun-2025 02:10:03.172 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [/sysx/apache-tomcat-9.0.96/webapps/examples]
26-Jun-2025 02:10:03.177 WARNING [main] org.apache.catalina.core.StandardContext.postWorkDirectory Failed to create work directory [/sysx/apache-tomcat-9.0.96/work/Catalina/localhost/examples] for context [/examples]
26-Jun-2025 02:10:03.395 SEVERE [main] org.apache.jasper.EmbeddedServletOptions.<init> The scratchDir you specified: [/sysx/apache-tomcat-9.0.96/work/Catalina/localhost/examples] is unusable.
26-Jun-2025 02:10:03.396 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [/sysx/apache-tomcat-9.0.96/webapps/examples] has finished in [224] ms
26-Jun-2025 02:10:03.396 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [/sysx/apache-tomcat-9.0.96/webapps/host-manager]
26-Jun-2025 02:10:03.400 WARNING [main] org.apache.catalina.core.StandardContext.postWorkDirectory Failed to create work directory [/sysx/apache-tomcat-9.0.96/work/Catalina/localhost/host-manager] for context [/host-manager]
26-Jun-2025 02:10:03.414 SEVERE [main] org.apache.jasper.EmbeddedServletOptions.<init> The scratchDir you specified: [/sysx/apache-tomcat-9.0.96/work/Catalina/localhost/host-manager] is unusable.
26-Jun-2025 02:10:03.415 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [/sysx/apache-tomcat-9.0.96/webapps/host-manager] has finished in [19] ms
26-Jun-2025 02:10:03.415 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [/sysx/apache-tomcat-9.0.96/webapps/docs]
26-Jun-2025 02:10:03.418 WARNING [main] org.apache.catalina.core.StandardContext.postWorkDirectory Failed to create work directory [/sysx/apache-tomcat-9.0.96/work/Catalina/localhost/docs] for context [/docs]
26-Jun-2025 02:10:03.427 SEVERE [main] org.apache.jasper.EmbeddedServletOptions.<init> The scratchDir you specified: [/sysx/apache-tomcat-9.0.96/work/Catalina/localhost/docs] is unusable.
26-Jun-2025 02:10:03.428 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [/sysx/apache-tomcat-9.0.96/webapps/docs] has finished in [13] ms
26-Jun-2025 02:10:03.428 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [/sysx/apache-tomcat-9.0.96/webapps/manager]
26-Jun-2025 02:10:03.432 WARNING [main] org.apache.catalina.core.StandardContext.postWorkDirectory Failed to create work directory [/sysx/apache-tomcat-9.0.96/work/Catalina/localhost/manager] for context [/manager]
26-Jun-2025 02:10:03.444 SEVERE [main] org.apache.jasper.EmbeddedServletOptions.<init> The scratchDir you specified: [/sysx/apache-tomcat-9.0.96/work/Catalina/localhost/manager] is unusable.
26-Jun-2025 02:10:03.445 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [/sysx/apache-tomcat-9.0.96/webapps/manager] has finished in [17] ms
26-Jun-2025 02:10:03.445 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [/sysx/apache-tomcat-9.0.96/webapps/tst]
26-Jun-2025 02:10:03.447 WARNING [main] org.apache.catalina.core.StandardContext.postWorkDirectory Failed to create work directory [/sysx/apache-tomcat-9.0.96/work/Catalina/localhost/tst] for context [/tst]
26-Jun-2025 02:10:03.456 SEVERE [main] org.apache.jasper.EmbeddedServletOptions.<init> The scratchDir you specified: [/sysx/apache-tomcat-9.0.96/work/Catalina/localhost/tst] is unusable.
26-Jun-2025 02:10:03.457 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [/sysx/apache-tomcat-9.0.96/webapps/tst] has finished in [12] ms
26-Jun-2025 02:10:03.461 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler ["https-jsse-nio-8443"]
26-Jun-2025 02:10:03.473 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [656] milliseconds
26-Jun-2025 02:10:03.476 SEVERE [main] org.apache.catalina.core.StandardServer.await Failed to create server shutdown socket on address [localhost] and port [8005] (base port [8005] and offset [0])
	java.net.BindException: Address already in use (Bind failed)
		at java.net.PlainSocketImpl.socketBind(Native Method)
		at java.net.AbstractPlainSocketImpl.bind(AbstractPlainSocketImpl.java:387)
		at java.net.ServerSocket.bind(ServerSocket.java:390)
		at java.net.ServerSocket.<init>(ServerSocket.java:252)
		at org.apache.catalina.core.StandardServer.await(StandardServer.java:537)
		at org.apache.catalina.startup.Catalina.await(Catalina.java:825)
		at org.apache.catalina.startup.Catalina.start(Catalina.java:773)
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
		at java.lang.reflect.Method.invoke(Method.java:498)
		at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345)
		at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:473)
26-Jun-2025 02:10:03.477 INFO [main] org.apache.coyote.AbstractProtocol.pause Pausing ProtocolHandler ["http-nio-8080"]
26-Jun-2025 02:10:03.477 INFO [main] org.apache.coyote.AbstractProtocol.pause Pausing ProtocolHandler ["https-jsse-nio-8443"]
26-Jun-2025 02:10:03.481 INFO [main] org.apache.catalina.core.StandardService.stopInternal Stopping service [Catalina]
26-Jun-2025 02:10:03.502 INFO [main] org.apache.coyote.AbstractProtocol.stop Stopping ProtocolHandler ["https-jsse-nio-8443"]
26-Jun-2025 02:10:03.514 INFO [main] org.apache.coyote.AbstractProtocol.stop Stopping ProtocolHandler ["http-nio-8080"]
26-Jun-2025 02:10:03.516 INFO [main] org.apache.coyote.AbstractProtocol.destroy Destroying ProtocolHandler ["http-nio-8080"]
26-Jun-2025 02:10:03.517 INFO [main] org.apache.coyote.AbstractProtocol.destroy Destroying ProtocolHandler ["https-jsse-nio-8443"]
26-Jun-2025 10:51:36.472 INFO [http-nio-8080-exec-1291] org.apache.catalina.startup.HostConfig.deployWAR Deploying web application archive [/sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40_231642.war]
26-Jun-2025 10:51:36.474 WARNING [http-nio-8080-exec-1291] org.apache.tomcat.util.digester.SetPropertiesRule.begin Match [Context] failed to set property [antiJARLocking] to [true]
26-Jun-2025 10:51:37.561 INFO [http-nio-8080-exec-1291] org.apache.jasper.servlet.TldScanner.scanJars At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
26-Jun-2025 10:51:38.428 WARNING [http-nio-8080-exec-1291] com.sun.xml.ws.transport.http.DeploymentDescriptorParser.parseAdapters WSSERVLET17: duplicate endpoint name
26-Jun-2025 10:51:38.481 WARNING [http-nio-8080-exec-1291] com.sun.xml.ws.transport.http.DeploymentDescriptorParser.parseAdapters WSSERVLET17: duplicate endpoint name
26-Jun-2025 10:51:38.563 WARNING [http-nio-8080-exec-1291] com.sun.xml.ws.transport.http.servlet.WSServletDelegate.registerEndpointUrlPattern WSSERVLET26: duplicate URL pattern in endpoint: wsFacturacionElectronicaCFDI
26-Jun-2025 10:51:38.563 WARNING [http-nio-8080-exec-1291] com.sun.xml.ws.transport.http.servlet.WSServletDelegate.registerEndpointUrlPattern WSSERVLET26: duplicate URL pattern in endpoint: wsFacturacionElectronicaConsultaCatalogos
26-Jun-2025 10:51:38.563 INFO [http-nio-8080-exec-1291] com.sun.xml.ws.transport.http.servlet.WSServletDelegate.<init> WSSERVLET14: JAX-WS servlet initializing
26-Jun-2025 10:51:38.563 INFO [http-nio-8080-exec-1291] com.sun.xml.ws.transport.http.servlet.WSServletContextListener.contextInitialized WSSERVLET12: JAX-WS context listener initializing
26-Jun-2025 10:51:38.564 INFO [http-nio-8080-exec-1291] com.sun.xml.ws.transport.http.servlet.WSServletContextListener.contextInitialized WSSERVLET12: JAX-WS context listener initializing
26-Jun-2025 10:51:38.567 INFO [http-nio-8080-exec-1291] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive [/sysx/apache-tomcat-9.0.96/webapps/FacturacionElectronicaCFDI40_231642.war] has finished in [2,095] ms
26-Jun-2025 13:33:02.961 INFO [http-nio-8080-exec-1292] org.apache.jasper.servlet.TldScanner.scanJars At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
26-Jun-2025 13:33:02.962 SEVERE [http-nio-8080-exec-1292] org.apache.catalina.core.StandardContext.startInternal One or more listeners failed to start. Full details will be found in the appropriate container log file
26-Jun-2025 13:33:02.962 SEVERE [http-nio-8080-exec-1292] org.apache.catalina.core.StandardContext.startInternal Context [/ws-conciliacion] startup failed due to previous errors
