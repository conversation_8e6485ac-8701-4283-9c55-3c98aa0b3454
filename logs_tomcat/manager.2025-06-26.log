26-Jun-2025 10:32:02.708 INFO [http-nio-8080-exec-1295] org.apache.catalina.core.ApplicationContext.log HTMLManager: list: Listing contexts for virtual host 'localhost'
26-Jun-2025 10:38:37.132 SEVERE [http-nio-8080-exec-1291] org.apache.catalina.core.ApplicationContext.log HTMLManager: FALLO - Falló Carga de Despliegue, Excepción: [Processing of multipart/form-data request failed. java.io.EOFException]
	org.apache.tomcat.util.http.fileupload.impl.IOFileUploadException: Processing of multipart/form-data request failed. java.io.EOFException
		at org.apache.tomcat.util.http.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:296)
		at org.apache.catalina.connector.Request.parseParts(Request.java:2624)
		at org.apache.catalina.connector.Request.parseParameters(Request.java:2957)
		at org.apache.catalina.connector.Request.getParameter(Request.java:1087)
		at org.apache.catalina.connector.RequestFacade.getParameter(RequestFacade.java:309)
		at org.apache.catalina.filters.CsrfPreventionFilter.doFilter(CsrfPreventionFilter.java:334)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.filters.HttpHeaderSecurityFilter.doFilter(HttpHeaderSecurityFilter.java:129)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:597)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:660)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:383)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:937)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.lang.Thread.run(Thread.java:750)
	Caused by: org.apache.catalina.connector.ClientAbortException: java.io.EOFException
		at org.apache.catalina.connector.InputBuffer.realReadBytes(InputBuffer.java:312)
		at org.apache.catalina.connector.InputBuffer.checkByteBufferEof(InputBuffer.java:615)
		at org.apache.catalina.connector.InputBuffer.read(InputBuffer.java:358)
		at org.apache.catalina.connector.CoyoteInputStream.read(CoyoteInputStream.java:132)
		at java.io.FilterInputStream.read(FilterInputStream.java:133)
		at org.apache.tomcat.util.http.fileupload.util.LimitedInputStream.read(LimitedInputStream.java:132)
		at org.apache.tomcat.util.http.fileupload.MultipartStream$ItemInputStream.makeAvailable(MultipartStream.java:953)
		at org.apache.tomcat.util.http.fileupload.MultipartStream$ItemInputStream.read(MultipartStream.java:857)
		at java.io.FilterInputStream.read(FilterInputStream.java:133)
		at org.apache.tomcat.util.http.fileupload.util.LimitedInputStream.read(LimitedInputStream.java:132)
		at java.io.FilterInputStream.read(FilterInputStream.java:107)
		at org.apache.tomcat.util.http.fileupload.util.Streams.copy(Streams.java:96)
		at org.apache.tomcat.util.http.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:292)
		... 30 more
		Suppressed: org.apache.catalina.connector.ClientAbortException: java.io.EOFException
			at org.apache.catalina.connector.InputBuffer.realReadBytes(InputBuffer.java:312)
			at org.apache.catalina.connector.InputBuffer.checkByteBufferEof(InputBuffer.java:615)
			at org.apache.catalina.connector.InputBuffer.read(InputBuffer.java:358)
			at org.apache.catalina.connector.CoyoteInputStream.read(CoyoteInputStream.java:132)
			at java.io.FilterInputStream.read(FilterInputStream.java:133)
			at org.apache.tomcat.util.http.fileupload.util.LimitedInputStream.read(LimitedInputStream.java:132)
			at org.apache.tomcat.util.http.fileupload.MultipartStream$ItemInputStream.makeAvailable(MultipartStream.java:953)
			at org.apache.tomcat.util.http.fileupload.MultipartStream$ItemInputStream.close(MultipartStream.java:897)
			at org.apache.tomcat.util.http.fileupload.MultipartStream$ItemInputStream.close(MultipartStream.java:876)
			at java.io.FilterInputStream.close(FilterInputStream.java:181)
			at org.apache.tomcat.util.http.fileupload.util.LimitedInputStream.close(LimitedInputStream.java:163)
			at org.apache.tomcat.util.http.fileupload.util.Streams.copy(Streams.java:92)
			... 31 more
		Caused by: java.io.EOFException
			at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1320)
			at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.read(NioEndpoint.java:1231)
			at org.apache.coyote.http11.Http11InputBuffer.fill(Http11InputBuffer.java:793)
			at org.apache.coyote.http11.Http11InputBuffer.access$400(Http11InputBuffer.java:41)
			at org.apache.coyote.http11.Http11InputBuffer$SocketInputBuffer.doRead(Http11InputBuffer.java:1199)
			at org.apache.coyote.http11.filters.IdentityInputFilter.doRead(IdentityInputFilter.java:96)
			at org.apache.coyote.http11.Http11InputBuffer.doRead(Http11InputBuffer.java:244)
			at org.apache.coyote.Request.doRead(Request.java:626)
			at org.apache.catalina.connector.InputBuffer.realReadBytes(InputBuffer.java:303)
			... 42 more
	Caused by: java.io.EOFException
		at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1320)
		at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.read(NioEndpoint.java:1231)
		at org.apache.coyote.http11.Http11InputBuffer.fill(Http11InputBuffer.java:793)
		at org.apache.coyote.http11.Http11InputBuffer.access$400(Http11InputBuffer.java:41)
		at org.apache.coyote.http11.Http11InputBuffer$SocketInputBuffer.doRead(Http11InputBuffer.java:1199)
		at org.apache.coyote.http11.filters.IdentityInputFilter.doRead(IdentityInputFilter.java:96)
		at org.apache.coyote.http11.Http11InputBuffer.doRead(Http11InputBuffer.java:244)
		at org.apache.coyote.Request.doRead(Request.java:626)
		at org.apache.catalina.connector.InputBuffer.realReadBytes(InputBuffer.java:303)
		... 42 more
26-Jun-2025 10:38:37.132 INFO [http-nio-8080-exec-1291] org.apache.catalina.core.ApplicationContext.log HTMLManager: list: Listing contexts for virtual host 'localhost'
26-Jun-2025 10:48:16.911 INFO [http-nio-8080-exec-1298] org.apache.catalina.core.ApplicationContext.log HTMLManager: list: Listing contexts for virtual host 'localhost'
26-Jun-2025 10:48:23.769 INFO [http-nio-8080-exec-1293] org.apache.catalina.core.ApplicationContext.log HTMLManager: list: Listing contexts for virtual host 'localhost'
26-Jun-2025 10:51:38.567 INFO [http-nio-8080-exec-1291] org.apache.catalina.core.ApplicationContext.log HTMLManager: list: Listing contexts for virtual host 'localhost'
26-Jun-2025 13:32:42.417 INFO [http-nio-8080-exec-1292] org.apache.catalina.core.ApplicationContext.log HTMLManager: list: Listing contexts for virtual host 'localhost'
26-Jun-2025 13:32:56.017 INFO [http-nio-8080-exec-1297] org.apache.catalina.core.ApplicationContext.log HTMLManager: list: Listing contexts for virtual host 'localhost'
26-Jun-2025 13:33:01.913 INFO [http-nio-8080-exec-1292] org.apache.catalina.core.ApplicationContext.log HTMLManager: start: Starting web application '/ws-conciliacion'
26-Jun-2025 13:33:02.977 INFO [http-nio-8080-exec-1292] org.apache.catalina.core.ApplicationContext.log HTMLManager: list: Listing contexts for virtual host 'localhost'
