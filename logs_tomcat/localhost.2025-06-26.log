26-Jun-2025 02:10:03.329 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextInitialized()
26-Jun-2025 02:10:03.329 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextInitialized()
26-Jun-2025 02:10:03.330 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: attributeAdded('StockTicker', 'async.Stockticker@5b247367')
26-Jun-2025 02:10:03.497 INFO [main] org.apache.catalina.core.ApplicationContext.log SessionListener: contextDestroyed()
26-Jun-2025 02:10:03.497 INFO [main] org.apache.catalina.core.ApplicationContext.log ContextListener: contextDestroyed()
26-Jun-2025 13:33:02.962 SEVERE [http-nio-8080-exec-1292] org.apache.catalina.core.StandardContext.listenerStart Error configuring application listener of class [com.coppel.wsconciliacion.config.PublicadorWs]
	java.lang.UnsupportedClassVersionError: com/coppel/wsconciliacion/config/PublicadorWs has been compiled by a more recent version of the Java Runtime (class file version 55.0), this version of the Java Runtime only recognizes class file versions up to 52.0 (unable to load class [com.coppel.wsconciliacion.config.PublicadorWs])
		at org.apache.catalina.loader.WebappClassLoaderBase.findClassInternal(WebappClassLoaderBase.java:2291)
		at org.apache.catalina.loader.WebappClassLoaderBase.findClass(WebappClassLoaderBase.java:797)
		at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1272)
		at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1120)
		at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:488)
		at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:470)
		at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:142)
		at org.apache.catalina.core.StandardContext.listenerStart(StandardContext.java:3932)
		at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4446)
		at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:164)
		at org.apache.catalina.manager.ManagerServlet.start(ManagerServlet.java:1303)
		at org.apache.catalina.manager.HTMLManagerServlet.start(HTMLManagerServlet.java:642)
		at org.apache.catalina.manager.HTMLManagerServlet.doPost(HTMLManagerServlet.java:188)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
		at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.filters.CsrfPreventionFilter.doFilter(CsrfPreventionFilter.java:430)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.filters.HttpHeaderSecurityFilter.doFilter(HttpHeaderSecurityFilter.java:129)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:597)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:660)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:383)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:937)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.lang.Thread.run(Thread.java:750)
26-Jun-2025 13:33:02.962 SEVERE [http-nio-8080-exec-1292] org.apache.catalina.core.StandardContext.listenerStart Skipped installing application listeners due to previous error(s)
