#!/bin/bash

# Script para probar el Web Service de Conciliación
# Autor: Asistente IA
# Fecha: $(date)

echo "=========================================="
echo "  PRUEBAS DEL WEB SERVICE DE CONCILIACIÓN"
echo "=========================================="

# Configuración
WEBSERVICE_URL="http://localhost:8080/ws-conciliacion/services/WsConciliacion"
WSDL_URL="http://localhost:8080/ws-conciliacion/services/WsConciliacion?wsdl"

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Función para mostrar mensajes
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar si curl está instalado
if ! command -v curl &> /dev/null; then
    print_error "curl no está instalado. Por favor instálalo para continuar."
    exit 1
fi

# Verificar conectividad al web service
print_status "Verificando conectividad al web service..."
if curl -s --head "$WSDL_URL" | head -n 1 | grep "HTTP/1.[01] [23].." > /dev/null; then
    print_status "✓ Web service está disponible"
else
    print_error "✗ No se puede conectar al web service en $WSDL_URL"
    print_warning "Asegúrate de que Tomcat esté ejecutándose y el WAR esté desplegado"
    exit 1
fi

# Función para hacer petición SOAP
make_soap_request() {
    local operation=$1
    local xml_file=$2
    local soap_action=$3
    
    echo ""
    print_status "Probando operación: $operation"
    echo "----------------------------------------"
    
    if [ -f "$xml_file" ]; then
        response=$(curl -s -X POST "$WEBSERVICE_URL" \
            -H "Content-Type: text/xml; charset=utf-8" \
            -H "SOAPAction: $soap_action" \
            -d @"$xml_file")
        
        if [ $? -eq 0 ]; then
            print_status "✓ Petición enviada exitosamente"
            echo "Respuesta:"
            echo "$response" | xmllint --format - 2>/dev/null || echo "$response"
        else
            print_error "✗ Error al enviar petición"
        fi
    else
        print_error "✗ Archivo XML no encontrado: $xml_file"
    fi
}

# Función para obtener WSDL
get_wsdl() {
    print_status "Obteniendo WSDL del web service..."
    echo "----------------------------------------"
    
    wsdl_content=$(curl -s "$WSDL_URL")
    if [ $? -eq 0 ]; then
        print_status "✓ WSDL obtenido exitosamente"
        echo "$wsdl_content" | xmllint --format - 2>/dev/null || echo "$wsdl_content"
    else
        print_error "✗ Error al obtener WSDL"
    fi
}

# Función para listar servicios disponibles
list_services() {
    print_status "Listando servicios disponibles..."
    echo "----------------------------------------"
    
    services_url="http://localhost:8080/ws-conciliacion/services"
    services_content=$(curl -s "$services_url")
    if [ $? -eq 0 ]; then
        print_status "✓ Servicios obtenidos exitosamente"
        echo "$services_content"
    else
        print_error "✗ Error al obtener lista de servicios"
    fi
}

# Menú principal
show_menu() {
    echo ""
    echo "Selecciona una opción:"
    echo "1) Obtener WSDL"
    echo "2) Listar servicios disponibles"
    echo "3) Probar generarConciliacion"
    echo "4) Probar obtenerUltimaFecha"
    echo "5) Probar consultarEstadoConciliacion"
    echo "6) Probar reenviarCorreo"
    echo "7) Probar reenviarArchivoConnect"
    echo "8) Ejecutar todas las pruebas"
    echo "9) Salir"
    echo ""
    read -p "Opción: " choice
}

# Ejecutar todas las pruebas
run_all_tests() {
    print_status "Ejecutando todas las pruebas..."
    echo "=========================================="
    
    make_soap_request "generarConciliacion" "ejemplos_peticiones/generar_conciliacion.xml" "generarConciliacion"
    make_soap_request "obtenerUltimaFecha" "ejemplos_peticiones/obtener_ultima_fecha.xml" "obtenerUltimaFecha"
    make_soap_request "consultarEstadoConciliacion" "ejemplos_peticiones/consultar_estado.xml" "consultarEstadoConciliacion"
    make_soap_request "reenviarCorreo" "ejemplos_peticiones/reenviar_correo.xml" "reenviarCorreo"
    make_soap_request "reenviarArchivoConnect" "ejemplos_peticiones/reenviar_archivo_connect.xml" "reenviarArchivoConnect"
    
    print_status "Todas las pruebas completadas"
}

# Función para configurar URL personalizada
configure_url() {
    echo ""
    print_status "Configuración actual:"
    echo "  Web Service URL: $WEBSERVICE_URL"
    echo "  WSDL URL: $WSDL_URL"
    echo ""
    read -p "¿Deseas cambiar la URL? (s/n): " change_url
    
    if [[ $change_url =~ ^[Ss]$ ]]; then
        read -p "Nueva URL base (ej: http://localhost:8080/ws-conciliacion): " new_base_url
        if [ ! -z "$new_base_url" ]; then
            WEBSERVICE_URL="$new_base_url/services/WsConciliacion"
            WSDL_URL="$new_base_url/services/WsConciliacion?wsdl"
            print_status "URL actualizada a: $WEBSERVICE_URL"
        fi
    fi
}

# Bucle principal
while true; do
    show_menu
    
    case $choice in
        1)
            get_wsdl
            ;;
        2)
            list_services
            ;;
        3)
            make_soap_request "generarConciliacion" "ejemplos_peticiones/generar_conciliacion.xml" "generarConciliacion"
            ;;
        4)
            make_soap_request "obtenerUltimaFecha" "ejemplos_peticiones/obtener_ultima_fecha.xml" "obtenerUltimaFecha"
            ;;
        5)
            make_soap_request "consultarEstadoConciliacion" "ejemplos_peticiones/consultar_estado.xml" "consultarEstadoConciliacion"
            ;;
        6)
            make_soap_request "reenviarCorreo" "ejemplos_peticiones/reenviar_correo.xml" "reenviarCorreo"
            ;;
        7)
            make_soap_request "reenviarArchivoConnect" "ejemplos_peticiones/reenviar_archivo_connect.xml" "reenviarArchivoConnect"
            ;;
        8)
            run_all_tests
            ;;
        9)
            print_status "Saliendo..."
            exit 0
            ;;
        *)
            print_error "Opción inválida"
            ;;
    esac
    
    echo ""
    read -p "Presiona Enter para continuar..."
done 