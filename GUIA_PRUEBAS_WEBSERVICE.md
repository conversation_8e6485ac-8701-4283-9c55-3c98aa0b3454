# Guía de Pruebas del Web Service de Conciliación

## Resumen Ejecutivo

Este documento proporciona instrucciones detalladas para probar el **Web Service de Conciliación** de Co<PERSON>, incluyendo todas las operaciones disponibles y diferentes métodos de prueba.

## Funcionalidad del Web Service

### ¿Qué hace este web service?

El **Web Service de Conciliación** es un sistema que:

1. **Concilia datos financieros** entre sistemas de ingresos y cajas de Coppel
2. **Genera archivos de conciliación** en formato PDF y texto
3. **Envía archivos por correo electrónico** a destinatarios configurados
4. **Transfiere archivos a sistemas externos** mediante SFTP/Connect Direct
5. **Proporciona consultas de estado** y fechas de última conciliación
6. **Permite reenvíos** en caso de errores

### Tecnologías utilizadas

- **Backend**: Java 11 + Apache Axis 2.0.0 + Jakarta EE
- **Base de datos**: PostgreSQL
- **Generación de PDFs**: iText 7
- **Transferencia de archivos**: JSch (SFTP) + JCIFS (SMB)
- **Correo electrónico**: Jakarta Mail
- **Frontend**: PHP + jQuery + HTML/CSS
- **Servidor**: Apache Tomcat

## Operaciones Disponibles

### 1. generarConciliacion()
**Propósito**: Inicia el proceso completo de conciliación
- **Entrada**: Ninguna
- **Salida**: String con formato "1|Mensaje éxito" o "2|Mensaje error"
- **Acción**: Procesa datos, genera archivos, envía correos y transfiere archivos

### 2. obtenerUltimaFecha()
**Propósito**: Obtiene la fecha de la última conciliación realizada
- **Entrada**: Ninguna
- **Salida**: String con la fecha en formato específico
- **Acción**: Consulta la base de datos para obtener la fecha más reciente

### 3. consultarEstadoConciliacion()
**Propósito**: Consulta el estado actual del proceso de conciliación
- **Entrada**: Ninguna
- **Salida**: String con el estado actual
- **Acción**: Lee archivos de estado para determinar el progreso

### 4. reenviarCorreo(int peticion)
**Propósito**: Reenvía correos o elimina archivos según el parámetro
- **Entrada**: 
  - `peticion = 1`: Reenviar correo
  - `peticion ≠ 1`: Eliminar archivos
- **Salida**: String con el resultado de la operación
- **Acción**: Reenvía archivos por correo o limpia archivos temporales

### 5. reenviarArchivoConnect(int peticion)
**Propósito**: Reenvía archivos al sistema Connect Direct
- **Entrada**:
  - `peticion = 1`: Reenviar archivo
  - `peticion ≠ 1`: Eliminar archivos
- **Salida**: String con el resultado de la operación
- **Acción**: Transfiere archivos por SFTP o limpia archivos temporales

## Configuración del Entorno

### Prerrequisitos

1. **Java 11** instalado y configurado
2. **Apache Tomcat 9+** instalado y ejecutándose
3. **PostgreSQL** configurado con las bases de datos:
   - `ingresos` (servidor: *************:5432)
   - `cajas` (servidor: ************:5432)
4. **Acceso a internet** para transferencias SFTP y correo

### Despliegue en Tomcat

1. **Copiar el WAR**:
   ```bash
   cp portalconciliacion/Codigo\ Servicio/wsConciliacion/pack/ws-conciliacion.war $TOMCAT_HOME/webapps/
   ```

2. **Reiniciar Tomcat**:
   ```bash
   $TOMCAT_HOME/bin/shutdown.sh
   $TOMCAT_HOME/bin/startup.sh
   ```

3. **Verificar despliegue**:
   - WSDL: `http://localhost:8080/ws-conciliacion/services/WsConciliacion?wsdl`
   - Servicios: `http://localhost:8080/ws-conciliacion/services`

## Métodos de Prueba

### Método 1: Script Automatizado (Recomendado)

#### Para Windows (PowerShell):
```powershell
# Ejecutar el script de PowerShell
.\script_pruebas_webservice.ps1
```

#### Para Linux/Mac (Bash):
```bash
# Dar permisos de ejecución
chmod +x script_pruebas_webservice.sh

# Ejecutar el script
./script_pruebas_webservice.sh
```

### Método 2: cURL Manual

#### Generar Conciliación:
```bash
curl -X POST http://localhost:8080/ws-conciliacion/services/WsConciliacion \
  -H "Content-Type: text/xml; charset=utf-8" \
  -H "SOAPAction: generarConciliacion" \
  -d @ejemplos_peticiones/generar_conciliacion.xml
```

#### Consultar Estado:
```bash
curl -X POST http://localhost:8080/ws-conciliacion/services/WsConciliacion \
  -H "Content-Type: text/xml; charset=utf-8" \
  -H "SOAPAction: consultarEstadoConciliacion" \
  -d @ejemplos_peticiones/consultar_estado.xml
```

#### Obtener Última Fecha:
```bash
curl -X POST http://localhost:8080/ws-conciliacion/services/WsConciliacion \
  -H "Content-Type: text/xml; charset=utf-8" \
  -H "SOAPAction: obtenerUltimaFecha" \
  -d @ejemplos_peticiones/obtener_ultima_fecha.xml
```

### Método 3: SoapUI

1. **Descargar e instalar SoapUI** desde https://www.soapui.org/
2. **Crear nuevo proyecto SOAP**
3. **Importar WSDL**: `http://localhost:8080/ws-conciliacion/services/WsConciliacion?wsdl`
4. **Crear requests** para cada operación usando los archivos XML de ejemplo
5. **Ejecutar las peticiones** y revisar las respuestas

### Método 4: Postman

1. **Crear nueva petición POST**
2. **URL**: `http://localhost:8080/ws-conciliacion/services/WsConciliacion`
3. **Headers**:
   - `Content-Type: text/xml; charset=utf-8`
   - `SOAPAction: [nombre_operacion]`
4. **Body**: Seleccionar "raw" y "XML", luego pegar el contenido de los archivos XML de ejemplo

### Método 5: Interfaz Web

1. **Configurar servidor web** (Apache/Nginx) con PHP
2. **Copiar archivos** de `portalconciliacion/Codigo Pagina/` al directorio web
3. **Configurar conexiones** de base de datos en PHP
4. **Acceder vía navegador** a la interfaz web

## Archivos de Ejemplo

Los archivos XML de ejemplo están ubicados en `ejemplos_peticiones/`:

- `generar_conciliacion.xml` - Petición para generar conciliación
- `obtener_ultima_fecha.xml` - Petición para obtener última fecha
- `consultar_estado.xml` - Petición para consultar estado
- `reenviar_correo.xml` - Petición para reenviar correo
- `reenviar_archivo_connect.xml` - Petición para reenviar archivo

## Respuestas Esperadas

### Respuesta Exitosa:
```xml
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
   <soapenv:Body>
      <ns:generarConciliacionResponse xmlns:ns="http://servicioweb.wsconciliacion.coppel.com">
         <ns:generarConciliacionReturn>1|Conciliación generada exitosamente</ns:generarConciliacionReturn>
      </ns:generarConciliacionResponse>
   </soapenv:Body>
</soapenv:Envelope>
```

### Respuesta con Error:
```xml
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
   <soapenv:Body>
      <ns:generarConciliacionResponse xmlns:ns="http://servicioweb.wsconciliacion.coppel.com">
         <ns:generarConciliacionReturn>2|Error al generar conciliación: [descripción del error]</ns:generarConciliacionReturn>
      </ns:generarConciliacionResponse>
   </soapenv:Body>
</soapenv:Envelope>
```

## Monitoreo y Logs

### Ubicación de Logs:
- **Log de conciliación**: `/sysx/progs/logs/conciliacion/logArchivoConciliacion`
- **Log de estado**: `/sysx/progs/logs/conciliacion/logEstadoConciliacion`
- **Log de correo**: `/sysx/progs/logs/conciliacion/logCorreoConciliacion`
- **Log de fecha**: `/sysx/progs/logs/conciliacion/logFechaConciliacion`
- **Archivo de estado**: `/sysx/progs/estatus/conciliacion/estatusConciliacion.txt`

### Formato de Logs:
```
[FECHA] [PROCESO] [MENSAJE]
```

### Comandos para monitorear:
```bash
# Ver logs en tiempo real
tail -f /sysx/progs/logs/conciliacion/logArchivoConciliacion

# Ver últimos 100 líneas
tail -n 100 /sysx/progs/logs/conciliacion/logArchivoConciliacion

# Buscar errores
grep "ERROR" /sysx/progs/logs/conciliacion/logArchivoConciliacion
```

## Troubleshooting

### Problemas Comunes

#### 1. Error de conexión a base de datos
**Síntomas**: Error "2|Error de conexión a base de datos"
**Solución**:
- Verificar que PostgreSQL esté ejecutándose
- Comprobar configuración en `config.properties`
- Validar credenciales y conectividad de red

#### 2. Error de permisos en archivos
**Síntomas**: Error "2|Error al crear el log"
**Solución**:
- Verificar permisos en directorios de logs
- Asegurar que el usuario de Tomcat tenga permisos de escritura

#### 3. Error de SFTP
**Síntomas**: Error "2|Error al depositar archivo en repositorio"
**Solución**:
- Verificar configuración de SFTP en la base de datos
- Comprobar conectividad al servidor remoto
- Validar credenciales SFTP

#### 4. Error de correo
**Síntomas**: Error "2|Error al enviar correo"
**Solución**:
- Verificar configuración del servidor SMTP
- Comprobar credenciales de correo
- Validar direcciones de correo en la configuración

### Verificación de Estado

#### Verificar si el servicio está ejecutándose:
```bash
# Verificar proceso de Tomcat
ps aux | grep tomcat

# Verificar puerto 8080
netstat -tlnp | grep 8080

# Verificar logs de Tomcat
tail -f $TOMCAT_HOME/logs/catalina.out
```

#### Verificar conectividad:
```bash
# Probar conectividad al web service
curl -I http://localhost:8080/ws-conciliacion/services/WsConciliacion?wsdl

# Probar conectividad a bases de datos
telnet 10.28.************
telnet ************ 5432
```

## Casos de Prueba Recomendados

### 1. Prueba Básica de Conectividad
1. Obtener WSDL
2. Listar servicios disponibles
3. Consultar estado de conciliación

### 2. Prueba de Operaciones Principales
1. Obtener última fecha
2. Generar conciliación
3. Consultar estado después de generar
4. Reenviar correo (peticion = 1)
5. Reenviar archivo connect (peticion = 1)

### 3. Prueba de Manejo de Errores
1. Reenviar correo (peticion = 0) - elimina archivos
2. Reenviar archivo connect (peticion = 0) - elimina archivos
3. Probar con configuraciones incorrectas

### 4. Prueba de Carga
1. Ejecutar múltiples peticiones simultáneas
2. Monitorear uso de memoria y CPU
3. Verificar logs para errores de concurrencia

## Consideraciones de Seguridad

1. **Credenciales**: Las contraseñas están encriptadas en `config.properties`
2. **Logs**: Todas las operaciones se registran para auditoría
3. **Validaciones**: Se realizan validaciones de datos antes de procesar
4. **Manejo de errores**: Implementado manejo robusto de excepciones
5. **Timeouts**: Configurados timeouts para operaciones de red

## Contacto y Soporte

Para soporte técnico o consultas sobre el web service:
- **Equipo de desarrollo**: Coppel IT
- **Documentación**: Revisar archivos de documentación en el proyecto
- **Logs**: Consultar archivos de log para diagnóstico de problemas

---

**Nota**: Esta guía asume que el web service está desplegado en `localhost:8080`. Ajusta las URLs según tu configuración específica. 