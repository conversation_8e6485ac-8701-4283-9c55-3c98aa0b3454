# Script para probar el Web Service de Conciliación (PowerShell)
# Autor: Asistente IA
# Fecha: $(Get-Date)

Write-Host "==========================================" -ForegroundColor Cyan
Write-Host "  PRUEBAS DEL WEB SERVICE DE CONCILIACIÓN" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan

# Configuración
$WEBSERVICE_URL = "http://localhost:8080/ws-conciliacion/services/WsConciliacion"
$WSDL_URL = "http://localhost:8080/ws-conciliacion/services/WsConciliacion?wsdl"

# Función para mostrar mensajes
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Verificar conectividad al web service
Write-Status "Verificando conectividad al web service..."
try {
    $response = Invoke-WebRequest -Uri $WSDL_URL -Method Head -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Status "✓ Web service está disponible"
    } else {
        Write-Error "✗ Web service no responde correctamente"
        exit 1
    }
} catch {
    Write-Error "✗ No se puede conectar al web service en $WSDL_URL"
    Write-Warning "Asegúrate de que Tomcat esté ejecutándose y el WAR esté desplegado"
    exit 1
}

# Función para hacer petición SOAP
function Invoke-SoapRequest {
    param(
        [string]$Operation,
        [string]$XmlFile,
        [string]$SoapAction
    )
    
    Write-Host ""
    Write-Status "Probando operación: $Operation"
    Write-Host "----------------------------------------" -ForegroundColor Gray
    
    if (Test-Path $XmlFile) {
        try {
            $xmlContent = Get-Content $XmlFile -Raw
            $headers = @{
                "Content-Type" = "text/xml; charset=utf-8"
                "SOAPAction" = $SoapAction
            }
            
            $response = Invoke-WebRequest -Uri $WEBSERVICE_URL -Method POST -Headers $headers -Body $xmlContent -UseBasicParsing
            
            if ($response.StatusCode -eq 200) {
                Write-Status "✓ Petición enviada exitosamente"
                Write-Host "Respuesta:" -ForegroundColor Cyan
                Write-Host $response.Content
            } else {
                Write-Error "✗ Error en la respuesta: $($response.StatusCode)"
            }
        } catch {
            Write-Error "✗ Error al enviar petición: $($_.Exception.Message)"
        }
    } else {
        Write-Error "✗ Archivo XML no encontrado: $XmlFile"
    }
}

# Función para obtener WSDL
function Get-Wsdl {
    Write-Status "Obteniendo WSDL del web service..."
    Write-Host "----------------------------------------" -ForegroundColor Gray
    
    try {
        $wsdlContent = Invoke-WebRequest -Uri $WSDL_URL -UseBasicParsing
        if ($wsdlContent.StatusCode -eq 200) {
            Write-Status "✓ WSDL obtenido exitosamente"
            Write-Host $wsdlContent.Content
        } else {
            Write-Error "✗ Error al obtener WSDL"
        }
    } catch {
        Write-Error "✗ Error al obtener WSDL: $($_.Exception.Message)"
    }
}

# Función para listar servicios disponibles
function Get-Services {
    Write-Status "Listando servicios disponibles..."
    Write-Host "----------------------------------------" -ForegroundColor Gray
    
    $servicesUrl = "http://localhost:8080/ws-conciliacion/services"
    try {
        $servicesContent = Invoke-WebRequest -Uri $servicesUrl -UseBasicParsing
        if ($servicesContent.StatusCode -eq 200) {
            Write-Status "✓ Servicios obtenidos exitosamente"
            Write-Host $servicesContent.Content
        } else {
            Write-Error "✗ Error al obtener lista de servicios"
        }
    } catch {
        Write-Error "✗ Error al obtener servicios: $($_.Exception.Message)"
    }
}

# Función para ejecutar todas las pruebas
function Invoke-AllTests {
    Write-Status "Ejecutando todas las pruebas..."
    Write-Host "==========================================" -ForegroundColor Cyan
    
    Invoke-SoapRequest "generarConciliacion" "ejemplos_peticiones\generar_conciliacion.xml" "generarConciliacion"
    Invoke-SoapRequest "obtenerUltimaFecha" "ejemplos_peticiones\obtener_ultima_fecha.xml" "obtenerUltimaFecha"
    Invoke-SoapRequest "consultarEstadoConciliacion" "ejemplos_peticiones\consultar_estado.xml" "consultarEstadoConciliacion"
    Invoke-SoapRequest "reenviarCorreo" "ejemplos_peticiones\reenviar_correo.xml" "reenviarCorreo"
    Invoke-SoapRequest "reenviarArchivoConnect" "ejemplos_peticiones\reenviar_archivo_connect.xml" "reenviarArchivoConnect"
    
    Write-Status "Todas las pruebas completadas"
}

# Función para mostrar menú
function Show-Menu {
    Write-Host ""
    Write-Host "Selecciona una opción:" -ForegroundColor Yellow
    Write-Host "1) Obtener WSDL"
    Write-Host "2) Listar servicios disponibles"
    Write-Host "3) Probar generarConciliacion"
    Write-Host "4) Probar obtenerUltimaFecha"
    Write-Host "5) Probar consultarEstadoConciliacion"
    Write-Host "6) Probar reenviarCorreo"
    Write-Host "7) Probar reenviarArchivoConnect"
    Write-Host "8) Ejecutar todas las pruebas"
    Write-Host "9) Salir"
    Write-Host ""
    $choice = Read-Host "Opción"
    return $choice
}

# Función para configurar URL personalizada
function Set-CustomUrl {
    Write-Host ""
    Write-Status "Configuración actual:"
    Write-Host "  Web Service URL: $WEBSERVICE_URL"
    Write-Host "  WSDL URL: $WSDL_URL"
    Write-Host ""
    $changeUrl = Read-Host "¿Deseas cambiar la URL? (s/n)"
    
    if ($changeUrl -eq "s" -or $changeUrl -eq "S") {
        $newBaseUrl = Read-Host "Nueva URL base (ej: http://localhost:8080/ws-conciliacion)"
        if ($newBaseUrl) {
            $script:WEBSERVICE_URL = "$newBaseUrl/services/WsConciliacion"
            $script:WSDL_URL = "$newBaseUrl/services/WsConciliacion?wsdl"
            Write-Status "URL actualizada a: $WEBSERVICE_URL"
        }
    }
}

# Bucle principal
do {
    $choice = Show-Menu
    
    switch ($choice) {
        "1" { Get-Wsdl }
        "2" { Get-Services }
        "3" { Invoke-SoapRequest "generarConciliacion" "ejemplos_peticiones\generar_conciliacion.xml" "generarConciliacion" }
        "4" { Invoke-SoapRequest "obtenerUltimaFecha" "ejemplos_peticiones\obtener_ultima_fecha.xml" "obtenerUltimaFecha" }
        "5" { Invoke-SoapRequest "consultarEstadoConciliacion" "ejemplos_peticiones\consultar_estado.xml" "consultarEstadoConciliacion" }
        "6" { Invoke-SoapRequest "reenviarCorreo" "ejemplos_peticiones\reenviar_correo.xml" "reenviarCorreo" }
        "7" { Invoke-SoapRequest "reenviarArchivoConnect" "ejemplos_peticiones\reenviar_archivo_connect.xml" "reenviarArchivoConnect" }
        "8" { Invoke-AllTests }
        "9" { 
            Write-Status "Saliendo..."
            exit 0 
        }
        default { Write-Error "Opción inválida" }
    }
    
    Write-Host ""
    Read-Host "Presiona Enter para continuar"
} while ($true) 